# NutriTrack Technical Documentation

## 1. Application Overview

NutriTrack is a mobile application developed using Kotlin and Jetpack Compose for Android. The application is designed to help users track and improve their nutritional habits by providing personalized insights based on their food preferences, lifestyle, and eating patterns.

### 1.1 Key Features

- User onboarding with personalized questionnaire
- Food quality scoring system
- Personalized nutrition insights
- Dashboard with nutritional metrics
- Multiple user personas based on eating habits

## 2. Architecture Overview

### 2.1 Project Structure

The application follows a standard Android project structure with clear separation of concerns:

```
com.fit2081.nutritrack/
├── data/                  # Data sources and repositories
├── models/                # Data models and entities
├── ui/                    # UI components and screens
│   ├── components/        # Reusable UI components
│   ├── screens/           # Screen-level composables
│   └── theme/             # Theming and styling
└── utils/                 # Utility functions and helpers
```

### 2.2 Technology Stack

- **Language**: Kotlin
- **UI Framework**: Jetpack Compose
- **Navigation**: Compose Navigation
- **Data Storage**: SharedPreferences
- **Minimum SDK**: 35 (Android 15)
- **Target SDK**: 35 (Android 15)

## 3. Core Components Analysis

### 3.1 Data Models

#### 3.1.1 QuestionnaireAnswers

```kotlin
data class QuestionnaireAnswers(
    val foodCategories: List<String>,
    val persona: String,
    val biggestMealTime: String,
    val sleepTime: String,
    val wakeUpTime: String
)
```

This data class stores user responses from the questionnaire, including food preferences and lifestyle information.

#### 3.1.2 Persona

```kotlin
data class Persona(
    val name: String,
    val description: String,
    @DrawableRes val imageResId: Int // Use appropriate drawable resource
) 
```

Represents different user personas based on eating habits and health consciousness.

#### 3.1.3 FoodScoreData

```kotlin
data class FoodScoreData(
    val userID: String,
    val gender: String,
    val totalScore: Double,
    val discretionaryScore: Double,
    val vegetablesScore: Double,
    val fruitScore: Double,
    val grainsScore: Double = 0.0,
    val wholeGrainsScore: Double = 0.0,
    val meatScore: Double = 0.0,
    val dairyScore: Double = 0.0,
    val waterScore: Double = 0.0,
    val sugarScore: Double = 0.0,
    val fatScore: Double = 0.0
)
```

Comprehensive model for storing nutritional scores across various food categories.

### 3.2 Navigation System

The application uses Jetpack Compose Navigation for screen transitions. The navigation graph is defined in MainActivity:

```kotlin
NavHost(
    navController = navController,
    startDestination = "welcome",
    modifier = Modifier.padding(innerPadding)
) {
    composable("welcome") {
        WelcomeScreen(onNavigateToLogin = {
            navController.navigate("login")
        })
    }
    composable("login") {
        LoginScreen(onLoginSuccess = {
            navController.navigate("questionnaire")
        })
    }
    // Additional routes...
}
```

The navigation flow follows this sequence:
1. Welcome Screen
2. Login Screen
3. Questionnaire Screen
4. Dashboard Screen
5. Additional screens (Insights, NutriCoach, Settings)

### 3.3 Data Management

#### 3.3.1 SharedPreferences Storage

User data is persisted using Android's SharedPreferences:

```kotlin
fun saveQuestionnaireData(context: Context, answers: QuestionnaireAnswers) {
    // Get user ID to create user-specific keys
    val userId = getUserId(context)
    // Implementation to save data using SharedPreferences
    val sharedPreferences = context.getSharedPreferences("NutriTrackPrefs", Context.MODE_PRIVATE)
    with(sharedPreferences.edit()) {
        putStringSet("${userId}_foodCategories", answers.foodCategories.toSet())
        putString("${userId}_persona", answers.persona)
        // Additional fields...
        apply()
    }
}
```

#### 3.3.2 CSV Data Processing

Nutritional score data is loaded from a CSV file in the assets folder:

```kotlin
fun getFoodScoreData(context: Context, userID: String): FoodScoreData? {
    try {
        val inputStream = context.assets.open("patient_data.csv")
        val reader = BufferedReader(InputStreamReader(inputStream))
        
        // Read header to understand column positions
        val header = reader.readLine()?.split(",") ?: return null
        // Process CSV data...
    } catch (e: Exception) {
        e.printStackTrace()
        return null
    }
}
```

## 4. UI Components

### 4.1 Screens

#### 4.1.1 QuestionnaireScreen

```kotlin
@Composable
fun QuestionnaireScreen(onSaveSuccess: () -> Unit) {
    // Food Categories selection
    // Persona selection
    // Time pickers for meal and sleep times
    // Save functionality
}
```

Collects user preferences through a multi-section form with various input types.

#### 4.1.2 DashboardScreen

```kotlin
@Composable
fun DashboardScreen(
    onEditQuestionnaire: () -> Unit,
    onNavigateToHome: () -> Unit,
    onNavigateToProfile: () -> Unit,
    onNavigateToInfo: () -> Unit,
    onNavigateToSettings: () -> Unit
) {
    // Display food quality score
    // Show category-specific scores
    // Provide score explanations
}
```

Presents nutritional insights with visual indicators and explanations.

### 4.2 Reusable Components

#### 4.2.1 NutriTrackNavigationBar

```kotlin
@Composable
fun NutriTrackNavigationBar(
    selectedItem: NavigationItem,
    onNavigateToHome: () -> Unit,
    onNavigateToInsights: () -> Unit,
    onNavigateToNutriCoach: () -> Unit,
    onNavigateToSettings: () -> Unit
) {
    // Navigation bar implementation
}
```

Bottom navigation component used across multiple screens.

#### 4.2.2 ScoreComponents

```kotlin
@Composable
fun ScoreCircle(
    score: Double,
    modifier: Modifier = Modifier
) {
    // Visual representation of score with color coding
}

@Composable
fun CategoryProgressBar(
    categoryName: String,
    score: Double,
    maxScore: Double
) {
    // Progress bar for category scores
}
```

Visualizes nutritional scores with color-coded indicators.

## 5. Theming and Styling

The application uses Material 3 design system with a custom health-focused color scheme:

```kotlin
NutriTrackTheme(
    darkTheme = isDarkTheme,
    dynamicColor = useDynamicColor
) {
    // Application content
}
```

## 6. Data Flow Analysis

### 6.1 Questionnaire Data Flow

1. User inputs preferences in QuestionnaireScreen
2. Data is validated and packaged into QuestionnaireAnswers object
3. saveQuestionnaireData() stores data in SharedPreferences
4. User is navigated to DashboardScreen

### 6.2 Food Score Calculation Flow

1. DashboardScreen loads with LaunchedEffect
2. getFoodScoreData() retrieves user-specific data from CSV
3. Score data is processed and displayed using ScoreCircle and CategoryProgressBar components
4. Score descriptions are generated based on numerical values

## 7. Performance Considerations

### 7.1 Data Loading

- CSV parsing is performed on the main thread, which could cause UI jank with large datasets
- SharedPreferences operations are relatively lightweight but not suitable for large data structures

### 7.2 UI Rendering

- Compose recomposition is optimized through proper state management
- LazyColumn is used for scrollable content to improve memory efficiency

## 8. Security Considerations

### 8.1 Data Storage

- User data is stored in SharedPreferences, which is not encrypted by default
- No sensitive personal information appears to be stored

### 8.2 User Authentication

- Basic login screen is implemented but authentication details are not persisted securely
- No API authentication mechanisms are visible in the codebase

## 9. Future Development Opportunities

### 9.1 Planned Features

- NutriCoach screen (placeholder exists but not implemented)
- Settings screen (placeholder exists but not implemented)

### 9.2 Technical Improvements

- Implement Room database for more robust data storage
- Add ViewModel layer for better separation of concerns
- Implement coroutines for background data processing
- Add unit and UI tests for critical components

## 10. Conclusion

NutriTrack is a well-structured Android application built with modern technologies. It follows a clean architecture with clear separation of UI, data, and business logic. The use of Jetpack Compose provides a modern, declarative UI approach, while the data management system, though simple, is effective for the current scope of the application.

The application demonstrates good practices in component reuse, state management, and UI design. Future development should focus on enhancing data management, adding more robust authentication, and implementing the planned feature screens.