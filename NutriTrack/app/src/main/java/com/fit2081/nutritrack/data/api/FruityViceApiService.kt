package com.fit2081.nutritrack.data.api

import com.fit2081.nutritrack.data.models.Fruit
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path

/**
 * Retrofit API service for FruityVice API
 * Base URL: https://www.fruityvice.com/api/
 */
interface FruityViceApiService {
    
    /**
     * Get all fruits
     * Endpoint: GET /fruit/all
     */
    @GET("fruit/all")
    suspend fun getAllFruits(): Response<List<Fruit>>
    
    /**
     * Get fruit by name
     * Endpoint: GET /fruit/{name}
     */
    @GET("fruit/{name}")
    suspend fun getFruitByName(@Path("name") name: String): Response<Fruit>
}
