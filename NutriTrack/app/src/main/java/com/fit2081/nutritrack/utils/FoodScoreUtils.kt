package com.fit2081.nutritrack.utils

import android.content.Context
import java.io.BufferedReader
import java.io.InputStreamReader

// Data class to hold Food Score information
data class FoodScoreData(
    val userID: String,
    val gender: String,
    val totalScore: Double,
    val discretionaryScore: Double,
    val vegetablesScore: Double,
    val fruitScore: Double,
    val grainsScore: Double = 0.0,
    val wholeGrainsScore: Double = 0.0,
    val meatScore: Double = 0.0,
    val dairyScore: Double = 0.0,
    val waterScore: Double = 0.0,
    val sugarScore: Double = 0.0,
    val fatScore: Double = 0.0
)

// Helper function to extract gender-specific score
private fun getGenderSpecificScore(
    values: List<String>,
    gender: String,
    maleIndex: Int,
    femaleIndex: Int
): Double {
    return if (gender.equals("Male", ignoreCase = true) && values.size > maleIndex)
        values[maleIndex].toDoubleOrNull() ?: 0.0
    else if (values.size > femaleIndex)
        values[femaleIndex].toDoubleOrNull() ?: 0.0
    else 0.0
}

// Function to retrieve food score data from CSV file using user ID
fun getFoodScoreData(context: Context, userID: String): FoodScoreData? {
    try {
        val inputStream = context.assets.open("patient_data.csv")
        val reader = BufferedReader(InputStreamReader(inputStream))
        
        // Read header to understand column positions
        val header = reader.readLine()?.split(",") ?: return null
        val userIdIndex = header.indexOf("User_ID")
        val sexIndex = header.indexOf("Sex")
        val maleTotalScoreIndex = header.indexOf("HEIFAtotalscoreMale")
        val femaleTotalScoreIndex = header.indexOf("HEIFAtotalscoreFemale")
        val maleDiscretionaryIndex = header.indexOf("DiscretionaryHEIFAscoreMale")
        val femaleDiscretionaryIndex = header.indexOf("DiscretionaryHEIFAscoreFemale")
        val maleVegetablesIndex = header.indexOf("VegetablesHEIFAscoreMale")
        val femaleVegetablesIndex = header.indexOf("VegetablesHEIFAscoreFemale")
        val maleFruitIndex = header.indexOf("FruitHEIFAscoreMale")
        val femaleFruitIndex = header.indexOf("FruitHEIFAscoreFemale")
        val maleGrainsIndex = header.indexOf("GrainsandcerealsHEIFAscoreMale")
        val femaleGrainsIndex = header.indexOf("GrainsandcerealsHEIFAscoreFemale")
        val maleWholeGrainsIndex = header.indexOf("WholegrainsHEIFAscoreMale")
        val femaleWholeGrainsIndex = header.indexOf("WholegrainsHEIFAscoreFemale")
        val maleMeatIndex = header.indexOf("MeatandalternativesHEIFAscoreMale")
        val femaleMeatIndex = header.indexOf("MeatandalternativesHEIFAscoreFemale")
        val maleDairyIndex = header.indexOf("DairyandalternativesHEIFAscoreMale")
        val femaleDairyIndex = header.indexOf("DairyandalternativesHEIFAscoreFemale")
        val maleWaterIndex = header.indexOf("WaterHEIFAscoreMale")
        val femaleWaterIndex = header.indexOf("WaterHEIFAscoreFemale")
        val maleSugarIndex = header.indexOf("SugarHEIFAscoreMale")
        val femaleSugarIndex = header.indexOf("SugarHEIFAscoreFemale")
        val maleFatIndex = header.indexOf("SaturatedFatHEIFAscoreMale")
        val femaleFatIndex = header.indexOf("SaturatedFatHEIFAscoreFemale")
        
        // Make sure all required indexes exist
        if (userIdIndex == -1 || sexIndex == -1 || 
            maleTotalScoreIndex == -1 || femaleTotalScoreIndex == -1) {
            return null
        }
        
        // Read each line to find matching user ID
        var line = reader.readLine()
        while (line != null) {
            val values = line.split(",")
            if (values.size > userIdIndex && values[userIdIndex] == userID) {
                val gender = if (values.size > sexIndex) values[sexIndex] else "Unknown"
                
                // Get all scores using the helper function
                val totalScore = getGenderSpecificScore(values, gender, maleTotalScoreIndex, femaleTotalScoreIndex)
                val discretionaryScore = getGenderSpecificScore(values, gender, maleDiscretionaryIndex, femaleDiscretionaryIndex)
                val vegetablesScore = getGenderSpecificScore(values, gender, maleVegetablesIndex, femaleVegetablesIndex)
                val fruitScore = getGenderSpecificScore(values, gender, maleFruitIndex, femaleFruitIndex)
                val grainsScore = getGenderSpecificScore(values, gender, maleGrainsIndex, femaleGrainsIndex)
                val wholeGrainsScore = getGenderSpecificScore(values, gender, maleWholeGrainsIndex, femaleWholeGrainsIndex)
                val meatScore = getGenderSpecificScore(values, gender, maleMeatIndex, femaleMeatIndex)
                val dairyScore = getGenderSpecificScore(values, gender, maleDairyIndex, femaleDairyIndex)
                val waterScore = getGenderSpecificScore(values, gender, maleWaterIndex, femaleWaterIndex)
                val sugarScore = getGenderSpecificScore(values, gender, maleSugarIndex, femaleSugarIndex)
                val fatScore = getGenderSpecificScore(values, gender, maleFatIndex, femaleFatIndex)
                
                reader.close()
                return FoodScoreData(
                    userID = userID,
                    gender = gender,
                    totalScore = totalScore,
                    discretionaryScore = discretionaryScore,
                    vegetablesScore = vegetablesScore,
                    fruitScore = fruitScore,
                    grainsScore = grainsScore,
                    wholeGrainsScore = wholeGrainsScore,
                    meatScore = meatScore,
                    dairyScore = dairyScore,
                    waterScore = waterScore,
                    sugarScore = sugarScore,
                    fatScore = fatScore
                )
            }
            line = reader.readLine()
        }
        
        reader.close()
        return null
    } catch (e: Exception) {
        e.printStackTrace()
        return null
    }
}

// Function to get a description based on food score
fun getFoodScoreDescription(score: Double): String {
    return when {
        score >= 80 -> "Excellent! Your eating habits are very healthy."
        score >= 70 -> "Very Good. Your diet is well-balanced."
        score >= 60 -> "Good. Your diet is on the right track, but there's room for improvement."
        score >= 50 -> "Fair. Consider making some changes to your diet."
        score >= 40 -> "Needs Improvement. Your diet could benefit from significant changes."
        else -> "Attention Required. Please consult with a nutritionist for guidance."
    }
}

// Helper function to get SharedPreferences user ID (for demo purposes)
fun getUserId(context: Context): String {
    // In a real app, this would be retrieved from user authentication
    // For demo purposes, using a hardcoded value or retrieving from SharedPreferences
    val sharedPreferences = context.getSharedPreferences("NutriTrackPrefs", Context.MODE_PRIVATE)
    return sharedPreferences.getString("userId", "1") ?: "1"
} 