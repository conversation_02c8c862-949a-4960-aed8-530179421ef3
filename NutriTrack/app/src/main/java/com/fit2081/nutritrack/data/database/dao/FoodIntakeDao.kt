package com.fit2081.nutritrack.data.database.dao

import androidx.room.*
import com.fit2081.nutritrack.data.database.entities.FoodIntakeEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface FoodIntakeDao {
    
    @Query("SELECT * FROM food_intake WHERE userId = :userId ORDER BY updatedAt DESC LIMIT 1")
    suspend fun getLatestFoodIntakeByUserId(userId: String): FoodIntakeEntity?
    
    @Query("SELECT * FROM food_intake WHERE userId = :userId ORDER BY updatedAt DESC LIMIT 1")
    fun getLatestFoodIntakeByUserIdFlow(userId: String): Flow<FoodIntakeEntity?>
    
    @Query("SELECT * FROM food_intake WHERE userId = :userId ORDER BY createdAt DESC")
    suspend fun getAllFoodIntakeByUserId(userId: String): List<FoodIntakeEntity>
    
    @Query("SELECT * FROM food_intake WHERE userId = :userId ORDER BY createdAt DESC")
    fun getAllFoodIntakeByUserIdFlow(userId: String): Flow<List<FoodIntakeEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertFoodIntake(foodIntake: FoodIntakeEntity): Long
    
    @Update
    suspend fun updateFoodIntake(foodIntake: FoodIntakeEntity)
    
    @Delete
    suspend fun deleteFoodIntake(foodIntake: FoodIntakeEntity)
    
    @Query("DELETE FROM food_intake WHERE userId = :userId")
    suspend fun deleteAllFoodIntakeByUserId(userId: String)
}
