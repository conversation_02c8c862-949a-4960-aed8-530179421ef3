package com.fit2081.nutritrack.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.fit2081.nutritrack.R

/**
 * Displays a category score with a progress bar
 */
@Composable
fun CategoryProgressBar(
    categoryName: String,
    score: Double,
    maxScore: Double
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = categoryName,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = String.format("%.1f", score) + " " + stringResource(R.string.max_score_label),
                style = MaterialTheme.typography.bodyMedium
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        LinearProgressIndicator(
            progress = (score / maxScore).toFloat().coerceIn(0f, 1f),
            modifier = Modifier.fillMaxWidth(),
            color = when {
                score >= maxScore * 0.7 -> Color(0xFF4CAF50) // Green
                score >= maxScore * 0.4 -> Color(0xFFFFC107) // Yellow
                else -> Color(0xFFF44336) // Red
            },
            trackColor = Color.LightGray
        )
    }
}

/**
 * Displays a score circle with color based on score value
 */
@Composable
fun ScoreCircle(
    score: Double,
    modifier: Modifier = Modifier
) {
    // Score background
    val scoreColor = when {
        score >= 70 -> Color(0xFF4CAF50) // Green
        score >= 50 -> Color(0xFFFFC107) // Yellow
        else -> Color(0xFFF44336) // Red
    }
    
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier.padding(8.dp)
    ) {
        Box(
            modifier = Modifier
                .background(
                    color = scoreColor,
                    shape = RoundedCornerShape(percent = 50)
                )
                .padding(32.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = String.format("%.1f", score),
                style = MaterialTheme.typography.headlineLarge,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
        }
    }
} 