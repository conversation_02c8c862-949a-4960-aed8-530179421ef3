package com.fit2081.nutritrack.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import com.fit2081.nutritrack.data.database.dao.FoodIntakeDao
import com.fit2081.nutritrack.data.database.dao.PatientDao
import com.fit2081.nutritrack.data.database.dao.UserDao
import com.fit2081.nutritrack.data.database.entities.FoodIntakeEntity
import com.fit2081.nutritrack.data.database.entities.PatientEntity
import com.fit2081.nutritrack.data.database.entities.UserEntity

@Database(
    entities = [
        PatientEntity::class,
        UserEntity::class,
        FoodIntakeEntity::class
    ],
    version = 2,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class NutriTrackDatabase : RoomDatabase() {

    abstract fun patientDao(): PatientDao
    abstract fun userDao(): UserDao
    abstract fun foodIntakeDao(): FoodIntakeDao

    companion object {
        @Volatile
        private var INSTANCE: NutriTrackDatabase? = null

        val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Remove the name column from users table
                database.execSQL("""
                    CREATE TABLE users_new (
                        userId TEXT NOT NULL PRIMARY KEY,
                        passwordHash TEXT NOT NULL,
                        isFirstLogin INTEGER NOT NULL,
                        createdAt INTEGER NOT NULL,
                        lastLoginAt INTEGER
                    )
                """.trimIndent())

                database.execSQL("""
                    INSERT INTO users_new (userId, passwordHash, isFirstLogin, createdAt, lastLoginAt)
                    SELECT userId, passwordHash, isFirstLogin, createdAt, lastLoginAt FROM users
                """.trimIndent())

                database.execSQL("DROP TABLE users")
                database.execSQL("ALTER TABLE users_new RENAME TO users")
            }
        }

        fun getDatabase(context: Context): NutriTrackDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    NutriTrackDatabase::class.java,
                    "nutritrack_database"
                )
                .addMigrations(MIGRATION_1_2)
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
