package com.fit2081.nutritrack.ui.screens

import android.app.TimePickerDialog
import android.widget.Toast
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.Button
import androidx.compose.material3.Checkbox
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.fit2081.nutritrack.R
import com.fit2081.nutritrack.data.personaDetailsList
import com.fit2081.nutritrack.models.Persona
import com.fit2081.nutritrack.models.QuestionnaireAnswers
import com.fit2081.nutritrack.ui.components.PersonaInfoModal
import com.fit2081.nutritrack.ui.theme.NutriTrackTheme
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.runtime.collectAsState
import com.fit2081.nutritrack.ui.viewmodel.AuthViewModel
import com.fit2081.nutritrack.ui.viewmodel.QuestionnaireState
import com.fit2081.nutritrack.ui.viewmodel.QuestionnaireViewModel
import java.util.Calendar
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
fun QuestionnaireScreen(
    onSaveSuccess: () -> Unit,
    authViewModel: AuthViewModel = hiltViewModel(),
    questionnaireViewModel: QuestionnaireViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val currentUserId by authViewModel.currentUserId.collectAsState()
    val questionnaireState by questionnaireViewModel.questionnaireState.collectAsState()
    val currentAnswers by questionnaireViewModel.currentAnswers.collectAsState()
    // Food Categories
    val foodCategories = listOf(
        "Fruits", "Vegetables", "Grains", "Red Meat", "Seafood", "Poultry", "Fish", "Eggs", "Nuts/Seeds",
    )
    val selectedFoodCategories = remember { mutableStateListOf<String>() }

    // Persona Selection - State for viewing details
    var showPersonaModal by remember { mutableStateOf(false) }
    var viewingPersonaInfo by remember { mutableStateOf<Persona?>(null) }

    // Persona Selection - State for final selection (dropdown)
    val personas = personaDetailsList.map { it.name } // Get names from the detailed list
    var selectedPersona by remember { mutableStateOf("") }
    var isDropdownExpanded by remember { mutableStateOf(false) }

    // Time Pickers
    var biggestMealTime by remember { mutableStateOf("") }
    var sleepTime by remember { mutableStateOf("") }
    var wakeUpTime by remember { mutableStateOf("") }

    // Load saved questionnaire data when the screen is first shown
    LaunchedEffect(currentUserId) {
        currentUserId?.let { userId ->
            questionnaireViewModel.loadQuestionnaireData(userId)
        }
    }

    // Update UI when data is loaded
    LaunchedEffect(currentAnswers) {
        currentAnswers?.let { answers ->
            selectedFoodCategories.clear()
            selectedFoodCategories.addAll(answers.foodCategories)
            selectedPersona = answers.persona
            biggestMealTime = answers.biggestMealTime
            sleepTime = answers.sleepTime
            wakeUpTime = answers.wakeUpTime
        }
    }

    // Handle questionnaire state changes
    LaunchedEffect(questionnaireState) {
        when (val currentState = questionnaireState) {
            is QuestionnaireState.Saved -> {
                Toast.makeText(context, "Data Saved!", Toast.LENGTH_SHORT).show()
                onSaveSuccess()
            }
            is QuestionnaireState.Error -> {
                Toast.makeText(context, currentState.message, Toast.LENGTH_LONG).show()
            }
            else -> {}
        }
    }

    // Function to show time picker dialog
    fun showTimePickerDialog(
        onTimeSelected: (String) -> Unit
    ) {
        val calendar = Calendar.getInstance()
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)

        TimePickerDialog(
            context,
            { _, selectedHour, selectedMinute ->
                val formattedTime = String.format(Locale.getDefault(), "%02d:%02d", selectedHour, selectedMinute)
                onTimeSelected(formattedTime)
            },
            hour,
            minute,
            true // 24 hour view
        ).show()
    }

    // Show Persona Info Modal when needed
    if (showPersonaModal && viewingPersonaInfo != null) {
        PersonaInfoModal(persona = viewingPersonaInfo!!, onDismiss = { showPersonaModal = false })
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item {
            // Food Categories
            Text(text = stringResource(R.string.food_categories_title))
            FlowRow(modifier = Modifier.fillMaxWidth()){
                foodCategories.forEach { category ->
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Checkbox(
                            checked = selectedFoodCategories.contains(category),
                            onCheckedChange = { isChecked ->
                                if (isChecked) {
                                    selectedFoodCategories.add(category)
                                } else {
                                    selectedFoodCategories.remove(category)
                                }
                            }
                        )
                        Text(text = category)
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp)) // Added spacer
        }
        item {
            // Persona Details Buttons
            Text(text = "Your Persona") // TODO: Move to strings.xml
            Text(text = "People can be broadly classified into 6 different types based on their eating preferences. Click on each button below to find out the different types, and select the type that best fits you!") // TODO: Move to strings.xml
            Spacer(modifier = Modifier.height(8.dp))
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally), // Center buttons
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                personaDetailsList.forEach { persona ->
                    Button(onClick = {
                        viewingPersonaInfo = persona
                        showPersonaModal = true
                    }) {
                        Text(persona.name)
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp)) // Added spacer
        }
        item {
             // Persona Selection Dropdown (for final choice)
            Text(text = stringResource(R.string.persona_selection_title)) // Changed title slightly
            Spacer(modifier = Modifier.height(8.dp))

             // Using OutlinedTextField for dropdown anchor as per Material Design recommendation
            ExposedDropdownMenuBox(
                expanded = isDropdownExpanded,
                onExpandedChange = { isDropdownExpanded = !isDropdownExpanded }
            ) {
                OutlinedTextField(
                    value = selectedPersona,
                    onValueChange = { }, // Don't allow manual changes
                    readOnly = true,
                    label = { Text("Which persona best fits you?") }, // TODO: Move to strings.xml
                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isDropdownExpanded) },
                    modifier = Modifier
                        .menuAnchor() // Anchor the dropdown menu
                        .fillMaxWidth()
                )

                ExposedDropdownMenu(
                    expanded = isDropdownExpanded,
                    onDismissRequest = { isDropdownExpanded = false }
                ) {
                    personas.forEach { persona ->
                        DropdownMenuItem(
                            text = { Text(text = persona) },
                            onClick = {
                                selectedPersona = persona
                                isDropdownExpanded = false
                            }
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp)) // Added spacer
        }
        item {
            // Time Pickers
            Text(text = stringResource(R.string.time_pickers_title))
            Spacer(modifier = Modifier.height(8.dp))

            // Use Column for better spacing and alignment of Time Pickers
            Column(modifier = Modifier.fillMaxWidth()) {
                 Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically // Align items vertically
                ) {
                    OutlinedTextField( // Changed to OutlinedTextField
                        value = biggestMealTime,
                        onValueChange = { },
                        label = { Text(stringResource(R.string.biggest_meal_time_label)) },
                        readOnly = true,
                        modifier = Modifier.weight(1f)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(onClick = {
                        showTimePickerDialog { selectedTime ->
                            biggestMealTime = selectedTime
                        }
                    }) {
                        Text(stringResource(id = R.string.select_time_button)) // Use string resource
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))

                 Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically // Align items vertically
                ) {
                    OutlinedTextField( // Changed to OutlinedTextField
                        value = sleepTime,
                        onValueChange = { },
                        label = { Text(stringResource(R.string.sleep_time_label)) },
                        readOnly = true,
                        modifier = Modifier.weight(1f)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(onClick = {
                        showTimePickerDialog { selectedTime ->
                            sleepTime = selectedTime
                        }
                    }) {
                        Text(stringResource(id = R.string.select_time_button)) // Use string resource
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))

                 Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically // Align items vertically
                ) {
                    OutlinedTextField( // Changed to OutlinedTextField
                        value = wakeUpTime,
                        onValueChange = { },
                        label = { Text(stringResource(R.string.wake_up_time_label)) },
                        readOnly = true,
                        modifier = Modifier.weight(1f)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(onClick = {
                        showTimePickerDialog { selectedTime ->
                            wakeUpTime = selectedTime
                        }
                    }) {
                        Text(stringResource(id = R.string.select_time_button)) // Use string resource
                    }
                }
            }
             Spacer(modifier = Modifier.height(16.dp)) // Added spacer
        }
        item {
            // Save Button
            Button(
                onClick = {
                    // Validation Check (Optional but recommended)
                    if (selectedFoodCategories.isEmpty() || selectedPersona.isBlank() || biggestMealTime.isBlank() || sleepTime.isBlank() || wakeUpTime.isBlank()) {
                        Toast.makeText(context, "Please fill all fields", Toast.LENGTH_SHORT).show() // TODO: Move to strings.xml
                        return@Button
                    }

                    val answers = QuestionnaireAnswers(
                        foodCategories = selectedFoodCategories.toList(), // Ensure it's an immutable list
                        persona = selectedPersona, // Use the persona from the dropdown
                        biggestMealTime = biggestMealTime,
                        sleepTime = sleepTime,
                        wakeUpTime = wakeUpTime
                    )
                    currentUserId?.let { userId ->
                        questionnaireViewModel.saveQuestionnaireData(userId, answers)
                    }
                },
                modifier = Modifier.fillMaxWidth(0.5f) // Make button wider
            ) {
                Text(stringResource(R.string.save_button_label))
            }
             Spacer(modifier = Modifier.height(16.dp)) // Add final spacer
        }
    }
}

// Add Preview if needed
@Preview(showBackground = true)
@Composable
fun QuestionnaireScreenPreview() {
    NutriTrackTheme {
        QuestionnaireScreen(onSaveSuccess = {})
    }
}