package com.fit2081.nutritrack.data.database.dao

import androidx.room.*
import com.fit2081.nutritrack.data.database.entities.PatientEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface PatientDao {
    
    @Query("SELECT * FROM patients WHERE userId = :userId")
    suspend fun getPatientByUserId(userId: String): PatientEntity?
    
    @Query("SELECT * FROM patients WHERE userId = :userId")
    fun getPatientByUserIdFlow(userId: String): Flow<PatientEntity?>
    
    @Query("SELECT * FROM patients WHERE phoneNumber = :phoneNumber AND userId = :userId")
    suspend fun validatePatientCredentials(phoneNumber: String, userId: String): PatientEntity?
    
    @Query("SELECT DISTINCT userId FROM patients")
    suspend fun getAllUserIds(): List<String>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPatient(patient: PatientEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPatients(patients: List<PatientEntity>)
    
    @Update
    suspend fun updatePatient(patient: PatientEntity)
    
    @Delete
    suspend fun deletePatient(patient: PatientEntity)
    
    @Query("DELETE FROM patients")
    suspend fun deleteAllPatients()
    
    @Query("SELECT COUNT(*) FROM patients")
    suspend fun getPatientCount(): Int
}
