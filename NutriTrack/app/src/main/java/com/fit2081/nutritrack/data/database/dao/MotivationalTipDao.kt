package com.fit2081.nutritrack.data.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import com.fit2081.nutritrack.data.database.entities.MotivationalTipEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface MotivationalTipDao {
    
    @Insert
    suspend fun insertTip(tip: MotivationalTipEntity): Long
    
    @Query("SELECT * FROM motivational_tips WHERE userId = :userId ORDER BY createdAt DESC")
    fun getAllTipsForUser(userId: String): Flow<List<MotivationalTipEntity>>
    
    @Query("SELECT * FROM motivational_tips WHERE userId = :userId ORDER BY createdAt DESC LIMIT 1")
    suspend fun getLatestTipForUser(userId: String): MotivationalTipEntity?
    
    @Query("SELECT COUNT(*) FROM motivational_tips WHERE userId = :userId")
    suspend fun getTipCountForUser(userId: String): Int
    
    @Query("DELETE FROM motivational_tips WHERE userId = :userId")
    suspend fun deleteAllTipsForUser(userId: String)
}
