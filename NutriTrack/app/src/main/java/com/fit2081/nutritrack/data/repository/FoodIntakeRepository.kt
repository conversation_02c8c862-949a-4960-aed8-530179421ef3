package com.fit2081.nutritrack.data.repository

import com.fit2081.nutritrack.data.database.dao.FoodIntakeDao
import com.fit2081.nutritrack.data.database.entities.FoodIntakeEntity
import com.fit2081.nutritrack.models.QuestionnaireAnswers
import com.google.gson.Gson
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FoodIntakeRepository @Inject constructor(
    private val foodIntakeDao: FoodIntakeDao
) {
    
    suspend fun getLatestFoodIntakeByUserId(userId: String): FoodIntakeEntity? {
        return foodIntakeDao.getLatestFoodIntakeByUserId(userId)
    }
    
    fun getLatestFoodIntakeByUserIdFlow(userId: String): Flow<FoodIntakeEntity?> {
        return foodIntakeDao.getLatestFoodIntakeByUserIdFlow(userId)
    }
    
    suspend fun getAllFoodIntakeByUserId(userId: String): List<FoodIntakeEntity> {
        return foodIntakeDao.getAllFoodIntakeByUserId(userId)
    }
    
    fun getAllFoodIntakeByUserIdFlow(userId: String): Flow<List<FoodIntakeEntity>> {
        return foodIntakeDao.getAllFoodIntakeByUserIdFlow(userId)
    }
    
    suspend fun insertFoodIntake(foodIntake: FoodIntakeEntity): Long {
        return foodIntakeDao.insertFoodIntake(foodIntake)
    }
    
    suspend fun updateFoodIntake(foodIntake: FoodIntakeEntity) {
        foodIntakeDao.updateFoodIntake(foodIntake)
    }
    
    suspend fun deleteFoodIntake(foodIntake: FoodIntakeEntity) {
        foodIntakeDao.deleteFoodIntake(foodIntake)
    }
    
    suspend fun deleteAllFoodIntakeByUserId(userId: String) {
        foodIntakeDao.deleteAllFoodIntakeByUserId(userId)
    }
    
    // Convert QuestionnaireAnswers to FoodIntakeEntity
    suspend fun saveQuestionnaireAnswers(userId: String, answers: QuestionnaireAnswers): Long {
        val foodCategoriesJson = Gson().toJson(answers.foodCategories)
        val foodIntake = FoodIntakeEntity(
            userId = userId,
            foodCategories = foodCategoriesJson,
            persona = answers.persona,
            biggestMealTime = answers.biggestMealTime,
            sleepTime = answers.sleepTime,
            wakeUpTime = answers.wakeUpTime
        )
        return insertFoodIntake(foodIntake)
    }
    
    // Convert FoodIntakeEntity to QuestionnaireAnswers
    fun getQuestionnaireAnswersFlow(userId: String): Flow<QuestionnaireAnswers?> {
        return getLatestFoodIntakeByUserIdFlow(userId).map { entity ->
            entity?.let { convertToQuestionnaireAnswers(it) }
        }
    }
    
    suspend fun getQuestionnaireAnswers(userId: String): QuestionnaireAnswers? {
        val entity = getLatestFoodIntakeByUserId(userId)
        return entity?.let { convertToQuestionnaireAnswers(it) }
    }
    
    private fun convertToQuestionnaireAnswers(entity: FoodIntakeEntity): QuestionnaireAnswers {
        val foodCategories = try {
            Gson().fromJson(entity.foodCategories, Array<String>::class.java).toList()
        } catch (e: Exception) {
            emptyList()
        }
        
        return QuestionnaireAnswers(
            foodCategories = foodCategories,
            persona = entity.persona,
            biggestMealTime = entity.biggestMealTime,
            sleepTime = entity.sleepTime,
            wakeUpTime = entity.wakeUpTime
        )
    }
}
