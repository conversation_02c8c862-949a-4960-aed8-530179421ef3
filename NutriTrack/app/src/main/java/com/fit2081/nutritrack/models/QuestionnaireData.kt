package com.fit2081.nutritrack.models

import androidx.annotation.DrawableRes

// Data class to hold questionnaire answers
data class QuestionnaireAnswers(
    val foodCategories: List<String>,
    val persona: String,
    val biggestMealTime: String,
    val sleepTime: String,
    val wakeUpTime: String
)

// Helper data class for Persona details
data class Persona(
    val name: String,
    val description: String,
    @DrawableRes val imageResId: Int // Use appropriate drawable resource
) 