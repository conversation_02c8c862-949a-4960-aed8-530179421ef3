package com.fit2081.nutritrack.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.fit2081.nutritrack.data.models.AiTipState
import com.fit2081.nutritrack.data.models.Fruit
import com.fit2081.nutritrack.data.models.FruitState
import com.fit2081.nutritrack.data.models.MotivationalTip
import com.fit2081.nutritrack.data.models.toDisplayModel
import com.fit2081.nutritrack.data.repository.FruitRepository
import com.fit2081.nutritrack.data.repository.MotivationalTipRepository
import com.fit2081.nutritrack.data.repository.PatientRepository
import com.fit2081.nutritrack.utils.FoodScoreData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class NutriCoachViewModel @Inject constructor(
    private val fruitRepository: FruitRepository,
    private val patientRepository: PatientRepository,
    private val motivationalTipRepository: MotivationalTipRepository
) : ViewModel() {

    private val _fruitState = MutableStateFlow<FruitState>(FruitState.Idle)
    val fruitState: StateFlow<FruitState> = _fruitState.asStateFlow()

    private val _foodScoreData = MutableStateFlow<FoodScoreData?>(null)
    val foodScoreData: StateFlow<FoodScoreData?> = _foodScoreData.asStateFlow()

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    // AI-related state
    private val _aiTipState = MutableStateFlow<AiTipState>(AiTipState.Idle)
    val aiTipState: StateFlow<AiTipState> = _aiTipState.asStateFlow()

    private val _allTips = MutableStateFlow<List<MotivationalTip>>(emptyList())
    val allTips: StateFlow<List<MotivationalTip>> = _allTips.asStateFlow()

    private val _showAllTipsDialog = MutableStateFlow(false)
    val showAllTipsDialog: StateFlow<Boolean> = _showAllTipsDialog.asStateFlow()

    /**
     * Load user's food score data to determine if fruit coaching is needed
     */
    fun loadFoodScoreData(userId: String) {
        viewModelScope.launch {
            try {
                val scoreData = patientRepository.getFoodScoreData(userId)
                _foodScoreData.value = scoreData
            } catch (e: Exception) {
                // Handle error silently for now
            }
        }
    }

    /**
     * Update search query
     */
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }

    /**
     * Search for fruit by name
     */
    fun searchFruit(fruitName: String) {
        if (fruitName.isBlank()) {
            _fruitState.value = FruitState.Idle
            return
        }

        viewModelScope.launch {
            _fruitState.value = FruitState.Loading

            fruitRepository.getFruitByName(fruitName)
                .onSuccess { fruit ->
                    _fruitState.value = FruitState.Success(fruit)
                }
                .onFailure { exception ->
                    _fruitState.value = FruitState.Error(
                        exception.message ?: "Failed to fetch fruit information"
                    )
                }
        }
    }

    /**
     * Clear current fruit search results
     */
    fun clearFruitSearch() {
        _fruitState.value = FruitState.Idle
        _searchQuery.value = ""
    }

    /**
     * Check if user's fruit score is non-optimal (below 70)
     */
    fun isFruitScoreNonOptimal(): Boolean {
        return _foodScoreData.value?.fruitScore?.let { it < 70.0 } ?: true
    }

    /**
     * Load all tips for user
     */
    fun loadAllTips(userId: String) {
        viewModelScope.launch {
            motivationalTipRepository.getAllTipsForUser(userId).collect { tips ->
                _allTips.value = tips.map { it.toDisplayModel() }
            }
        }
    }

    /**
     * Generate a new motivational message
     */
    fun generateMotivationalMessage(userId: String) {
        viewModelScope.launch {
            _aiTipState.value = AiTipState.Loading

            motivationalTipRepository.generateAndSaveMotivationalMessage(userId)
                .onSuccess { tip ->
                    _aiTipState.value = AiTipState.Success(tip)
                }
                .onFailure { exception ->
                    _aiTipState.value = AiTipState.Error(
                        exception.message ?: "Failed to generate motivational message"
                    )
                }
        }
    }

    /**
     * Generate a fun food tip
     */
    fun generateFunFoodTip(userId: String) {
        viewModelScope.launch {
            _aiTipState.value = AiTipState.Loading

            motivationalTipRepository.generateAndSaveFunFoodTip(userId)
                .onSuccess { tip ->
                    _aiTipState.value = AiTipState.Success(tip)
                }
                .onFailure { exception ->
                    _aiTipState.value = AiTipState.Error(
                        exception.message ?: "Failed to generate fun food tip"
                    )
                }
        }
    }

    /**
     * Show all tips dialog
     */
    fun showAllTipsDialog() {
        _showAllTipsDialog.value = true
    }

    /**
     * Hide all tips dialog
     */
    fun hideAllTipsDialog() {
        _showAllTipsDialog.value = false
    }

    /**
     * Clear AI tip state
     */
    fun clearAiTipState() {
        _aiTipState.value = AiTipState.Idle
    }
}
