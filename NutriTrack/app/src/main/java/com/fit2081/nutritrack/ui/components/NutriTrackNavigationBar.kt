package com.fit2081.nutritrack.ui.components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.material3.NavigationBarDefaults
import androidx.compose.ui.Modifier

enum class NavigationItem {
    HOME, INSIGHTS, NUTRICOACH, SETTINGS
}

@Composable
fun NutriTrackNavigationBar(
    selectedItem: NavigationItem,
    onNavigateToHome: () -> Unit,
    onNavigateToInsights: () -> Unit,
    onNavigateToNutriCoach: () -> Unit,
    onNavigateToSettings: () -> Unit
) {
    NavigationBar(
        containerColor = MaterialTheme.colorScheme.background
    ) {
        NavigationBarItem(
            selected = selectedItem == NavigationItem.HOME,
            onClick = onNavigateToHome,
            icon = { Icon(Icons.Filled.Home, contentDescription = "Home") },
            label = { Text("Home") }
        )
        NavigationBarItem(
            selected = selectedItem == NavigationItem.INSIGHTS,
            onClick = onNavigateToInsights,
            icon = { Icon(Icons.Filled.Info, contentDescription = "Insights") },
            label = { Text("Insights") }
        )
        NavigationBarItem(
            selected = selectedItem == NavigationItem.NUTRICOACH,
            onClick = onNavigateToNutriCoach,
            icon = { Icon(Icons.Filled.Person, contentDescription = "NutriCoach") },
            label = { Text("NutriCoach") }
        )
        NavigationBarItem(
            selected = selectedItem == NavigationItem.SETTINGS,
            onClick = onNavigateToSettings,
            icon = { Icon(Icons.Filled.Settings, contentDescription = "Settings") },
            label = { Text("Settings") }
        )
    }
} 