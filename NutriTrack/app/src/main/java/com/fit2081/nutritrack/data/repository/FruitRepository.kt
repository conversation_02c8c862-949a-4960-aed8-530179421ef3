package com.fit2081.nutritrack.data.repository

import com.fit2081.nutritrack.data.api.FruityViceApiService
import com.fit2081.nutritrack.data.models.Fruit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for fruit data from FruityVice API
 */
@Singleton
class FruitRepository @Inject constructor(
    private val apiService: FruityViceApiService
) {
    
    /**
     * Get all fruits from the API
     */
    suspend fun getAllFruits(): Result<List<Fruit>> {
        return try {
            val response = apiService.getAllFruits()
            if (response.isSuccessful) {
                val fruits = response.body() ?: emptyList()
                Result.success(fruits)
            } else {
                Result.failure(Exception("API Error: ${response.code()} ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get fruit by name from the API
     */
    suspend fun getFruitByName(name: String): Result<Fruit> {
        return try {
            val response = apiService.getFruitByName(name.lowercase().trim())
            if (response.isSuccessful) {
                val fruit = response.body()
                if (fruit != null) {
                    Result.success(fruit)
                } else {
                    Result.failure(Exception("Fruit not found"))
                }
            } else {
                Result.failure(Exception("API Error: ${response.code()} ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
