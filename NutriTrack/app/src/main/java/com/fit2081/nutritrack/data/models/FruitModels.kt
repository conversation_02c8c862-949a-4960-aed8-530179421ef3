package com.fit2081.nutritrack.data.models

import com.google.gson.annotations.SerializedName

/**
 * Data model for fruit nutrition information from FruityVice API
 */
data class Nutrition(
    @SerializedName("calories")
    val calories: Double,
    @SerializedName("fat")
    val fat: Double,
    @SerializedName("sugar")
    val sugar: Double,
    @SerializedName("carbohydrates")
    val carbohydrates: Double,
    @SerializedName("protein")
    val protein: Double
)

/**
 * Data model for fruit information from FruityVice API
 */
data class Fruit(
    @SerializedName("name")
    val name: String,
    @SerializedName("id")
    val id: Int,
    @SerializedName("family")
    val family: String,
    @SerializedName("order")
    val order: String,
    @SerializedName("genus")
    val genus: String,
    @SerializedName("nutritions")
    val nutritions: Nutrition
)

/**
 * UI state for fruit search and display
 */
sealed class FruitState {
    object Idle : FruitState()
    object Loading : FruitState()
    data class Success(val fruit: Fruit) : FruitState()
    data class Error(val message: String) : FruitState()
}
