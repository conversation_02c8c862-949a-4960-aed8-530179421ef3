package com.fit2081.nutritrack.ui.screens

import android.content.Context
import android.util.Log
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.fit2081.nutritrack.R
import com.fit2081.nutritrack.ui.theme.NutriTrackTheme
import java.io.BufferedReader
import java.io.InputStreamReader

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginScreen(modifier: Modifier = Modifier, onLoginSuccess: () -> Unit) {
    // State for the selected User ID
    var selectedUserId by remember { mutableStateOf("") }
    // State for the phone number text field
    var phoneNumber by remember { mutableStateOf(TextFieldValue("")) }
    // State for showing/hiding the error message
    var showError by remember { mutableStateOf(false) }
    // State for the dropdown menu
    var isDropdownExpanded by remember { mutableStateOf(false) }

    val context = LocalContext.current
    // Get user IDs from CSV
    val userIds = remember { getUserIdsFromCSV(context) }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // User ID Dropdown
        Button(onClick = { isDropdownExpanded = true }) {
            Text(text = if (selectedUserId.isNotEmpty()) selectedUserId else "Select User ID")
        }

        DropdownMenu(
            expanded = isDropdownExpanded,
            onDismissRequest = { isDropdownExpanded = false }
        ) {
            userIds.forEach { userId ->
                DropdownMenuItem(
                    text = { Text(text = userId) },
                    onClick = {
                        selectedUserId = userId
                        isDropdownExpanded = false
                    }
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Phone Number Input
        OutlinedTextField(
            value = phoneNumber,
            onValueChange = { phoneNumber = it },
            label = { Text("Phone Number") },
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Show error message
        if (showError) {
            Text(
                text = stringResource(R.string.login_error_message),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.error
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Continue Button
        Button(onClick = {
            // Perform login validation
            val isValid = validateCredentials(
                selectedUserId,
                phoneNumber.text,
                context
            )
            if (isValid) {
                showError = false
                // Save userId to SharedPreferences
                val sharedPreferences = context.getSharedPreferences("NutriTrackPrefs", Context.MODE_PRIVATE)
                sharedPreferences.edit().putString("userId", selectedUserId).apply()
                // Navigate to the next screen (if valid)
                onLoginSuccess()
            } else {
                showError = true
            }
        }) {
            Text("Continue")
        }
    }
}

fun validateCredentials(userId: String, phoneNumber: String, context: Context): Boolean {
    Log.d("LoginScreen", "Validating credentials: $userId, $phoneNumber")
    val assetManager = context.assets
    val inputStream = assetManager.open("patient_data.csv")
    val bufferedReader = BufferedReader(InputStreamReader(inputStream))

    var line: String? = bufferedReader.readLine() // Skip the header row
    while (bufferedReader.readLine().also { line = it } != null) {
        val columns = line?.split(",") ?: emptyList()
        if (columns.size >= 2) {
            val csvUserId = columns[1].trim()
            val csvPhoneNumber = columns[0].trim()

            if (csvUserId == userId && csvPhoneNumber == phoneNumber) {
                bufferedReader.close()
                Log.d("LoginScreen", "Credentials match!")
                return true
            }
        }
    }
    bufferedReader.close()
    Log.d("LoginScreen", "Credentials do not match.")
    return false
}
fun getUserIdsFromCSV(context: Context): List<String> {
    val userIds = mutableListOf<String>()
    val assetManager = context.assets
    try {
        val inputStream = assetManager.open("patient_data.csv")
        val bufferedReader = BufferedReader(InputStreamReader(inputStream))
        bufferedReader.readLine()// Skip header
        var line: String?
        while (bufferedReader.readLine().also { line = it } != null) {
            val columns = line?.split(",") ?: emptyList()
            if (columns.isNotEmpty()) {
                userIds.add(columns[1].trim())
            }
        }
        bufferedReader.close()
    } catch (e: Exception) {
        Log.e("LoginScreen", "Error reading CSV", e)
    }
    return userIds.distinct() // Ensure unique IDs
}


@Preview(showBackground = true)
@Composable
fun LoginScreenPreview() {
    NutriTrackTheme {
        LoginScreen(onLoginSuccess = {})
    }
}