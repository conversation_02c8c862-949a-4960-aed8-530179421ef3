package com.fit2081.nutritrack.ui.screens

import android.app.TimePickerDialog
import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import java.util.Calendar
import java.util.Locale
import com.fit2081.nutritrack.R
import com.fit2081.nutritrack.data.personaDetailsList
import com.fit2081.nutritrack.models.Persona
import com.fit2081.nutritrack.models.QuestionnaireAnswers
import com.fit2081.nutritrack.ui.components.PersonaInfoModal
import com.fit2081.nutritrack.ui.viewmodel.AuthViewModel
import com.fit2081.nutritrack.ui.viewmodel.QuestionnaireState
import com.fit2081.nutritrack.ui.viewmodel.QuestionnaireViewModel
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.material3.ExperimentalMaterial3Api

@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
fun NewQuestionnaireScreen(
    onSaveSuccess: () -> Unit,
    authViewModel: AuthViewModel = hiltViewModel(),
    questionnaireViewModel: QuestionnaireViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val currentUserId by authViewModel.currentUserId.collectAsState()
    val questionnaireState by questionnaireViewModel.questionnaireState.collectAsState()
    val currentAnswers by questionnaireViewModel.currentAnswers.collectAsState()

    // Food Categories
    val foodCategories = listOf(
        "Fruits", "Vegetables", "Grains", "Red Meat", "Seafood", "Poultry", "Fish", "Eggs", "Nuts/Seeds",
    )
    val selectedFoodCategories = remember { mutableStateListOf<String>() }

    // Persona Selection - State for viewing details
    var showPersonaModal by remember { mutableStateOf(false) }
    var viewingPersonaInfo by remember { mutableStateOf<Persona?>(null) }

    // Persona Selection - State for final selection (dropdown)
    val personas = personaDetailsList.map { it.name }
    var selectedPersona by remember { mutableStateOf("") }
    var isDropdownExpanded by remember { mutableStateOf(false) }

    // Time Pickers
    var biggestMealTime by remember { mutableStateOf("") }
    var sleepTime by remember { mutableStateOf("") }
    var wakeUpTime by remember { mutableStateOf("") }

    // Function to show time picker dialog
    fun showTimePickerDialog(onTimeSelected: (String) -> Unit) {
        val calendar = Calendar.getInstance()
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)

        TimePickerDialog(
            context,
            { _, selectedHour, selectedMinute ->
                val formattedTime = String.format(Locale.getDefault(), "%02d:%02d", selectedHour, selectedMinute)
                onTimeSelected(formattedTime)
            },
            hour,
            minute,
            true // 24 hour view
        ).show()
    }

    // Load saved questionnaire data when the screen is first shown
    LaunchedEffect(currentUserId) {
        currentUserId?.let { userId ->
            questionnaireViewModel.loadQuestionnaireData(userId)
        }
    }

    // Update UI when data is loaded
    LaunchedEffect(currentAnswers) {
        currentAnswers?.let { answers ->
            selectedFoodCategories.clear()
            selectedFoodCategories.addAll(answers.foodCategories)
            selectedPersona = answers.persona
            biggestMealTime = answers.biggestMealTime
            sleepTime = answers.sleepTime
            wakeUpTime = answers.wakeUpTime
        }
    }

    // Handle questionnaire state changes
    LaunchedEffect(questionnaireState) {
        when (val currentState = questionnaireState) {
            is QuestionnaireState.Saved -> {
                Toast.makeText(context, "数据已保存!", Toast.LENGTH_SHORT).show()
                onSaveSuccess()
            }
            is QuestionnaireState.Error -> {
                Toast.makeText(context, currentState.message, Toast.LENGTH_LONG).show()
            }
            else -> {}
        }
    }

    // Show persona modal
    if (showPersonaModal && viewingPersonaInfo != null) {
        PersonaInfoModal(
            persona = viewingPersonaInfo!!,
            onDismiss = { showPersonaModal = false }
        )
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item {
            // Food Categories
            Text(text = stringResource(R.string.food_categories_title))
            FlowRow(modifier = Modifier.fillMaxWidth()) {
                foodCategories.forEach { category ->
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Checkbox(
                            checked = selectedFoodCategories.contains(category),
                            onCheckedChange = { isChecked ->
                                if (isChecked) {
                                    selectedFoodCategories.add(category)
                                } else {
                                    selectedFoodCategories.remove(category)
                                }
                            }
                        )
                        Text(text = category)
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
        }

        item {
            // Persona Details Buttons
            Text(text = "您的饮食类型")
            Text(text = "人们可以根据饮食偏好大致分为6种不同类型。点击下面的按钮了解不同类型，并选择最适合您的类型！")
            Spacer(modifier = Modifier.height(8.dp))
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                personaDetailsList.forEach { persona ->
                    Button(onClick = {
                        viewingPersonaInfo = persona
                        showPersonaModal = true
                    }) {
                        Text(persona.name)
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
        }

        item {
            // Persona Selection Dropdown
            Text(text = stringResource(R.string.persona_selection_title))
            Spacer(modifier = Modifier.height(8.dp))

            ExposedDropdownMenuBox(
                expanded = isDropdownExpanded,
                onExpandedChange = { isDropdownExpanded = !isDropdownExpanded }
            ) {
                OutlinedTextField(
                    value = selectedPersona,
                    onValueChange = { },
                    readOnly = true,
                    label = { Text("哪种类型最适合您？") },
                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isDropdownExpanded) },
                    modifier = Modifier
                        .menuAnchor()
                        .fillMaxWidth()
                )

                ExposedDropdownMenu(
                    expanded = isDropdownExpanded,
                    onDismissRequest = { isDropdownExpanded = false }
                ) {
                    personas.forEach { persona ->
                        DropdownMenuItem(
                            text = { Text(persona) },
                            onClick = {
                                selectedPersona = persona
                                isDropdownExpanded = false
                            }
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
        }

        item {
            // Time Pickers
            Column(modifier = Modifier.fillMaxWidth()) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OutlinedTextField(
                        value = biggestMealTime,
                        onValueChange = { },
                        label = { Text(stringResource(R.string.biggest_meal_time_label)) },
                        readOnly = true,
                        modifier = Modifier.weight(1f)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(onClick = {
                        showTimePickerDialog { selectedTime ->
                            biggestMealTime = selectedTime
                        }
                    }) {
                        Text(stringResource(id = R.string.select_time_button))
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OutlinedTextField(
                        value = sleepTime,
                        onValueChange = { },
                        label = { Text(stringResource(R.string.sleep_time_label)) },
                        readOnly = true,
                        modifier = Modifier.weight(1f)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(onClick = {
                        showTimePickerDialog { selectedTime ->
                            sleepTime = selectedTime
                        }
                    }) {
                        Text(stringResource(id = R.string.select_time_button))
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OutlinedTextField(
                        value = wakeUpTime,
                        onValueChange = { },
                        label = { Text(stringResource(R.string.wake_up_time_label)) },
                        readOnly = true,
                        modifier = Modifier.weight(1f)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(onClick = {
                        showTimePickerDialog { selectedTime ->
                            wakeUpTime = selectedTime
                        }
                    }) {
                        Text(stringResource(id = R.string.select_time_button))
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
        }

        item {
            // Save Button
            Button(
                onClick = {
                    // Validation Check
                    if (selectedFoodCategories.isEmpty() || selectedPersona.isBlank() ||
                        biggestMealTime.isBlank() || sleepTime.isBlank() || wakeUpTime.isBlank()) {
                        Toast.makeText(context, "请填写所有字段", Toast.LENGTH_SHORT).show()
                        return@Button
                    }

                    val answers = QuestionnaireAnswers(
                        foodCategories = selectedFoodCategories.toList(),
                        persona = selectedPersona,
                        biggestMealTime = biggestMealTime,
                        sleepTime = sleepTime,
                        wakeUpTime = wakeUpTime
                    )

                    currentUserId?.let { userId ->
                        questionnaireViewModel.saveQuestionnaireData(userId, answers)
                    }
                },
                enabled = questionnaireState !is QuestionnaireState.Saving,
                modifier = Modifier.fillMaxWidth(0.5f)
            ) {
                if (questionnaireState is QuestionnaireState.Saving) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                } else {
                    Text(stringResource(R.string.save_button_label))
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}


