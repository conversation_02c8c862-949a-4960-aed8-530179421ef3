package com.fit2081.nutritrack.ui.theme

import androidx.compose.ui.graphics.Color



// Healthy Theme Light Mode Colors
val HealthyGreen = Color(0xFF4CAF50) // Primary Green
val HealthyGreenMedium = Color(0xFF66BB6A) // Medium Green (replaces blue)
val HealthyGreenLight = Color(0xFF81C784) // Secondary Green
val HealthyGreenPale = Color(0xFFA5D6A7) // Pale Green (replaces blue light)
val HealthyAccent = Color(0xFF8BC34A) // Accent Color

// Healthy Theme Dark Mode Colors
val HealthyGreenDark = Color(0xFF2E7D32) // Dark Green
val HealthyGreenDarkMedium = Color(0xFF388E3C) // Dark Medium Green (replaces dark blue)
val HealthyGreenDarkLight = Color(0xFF43A047) // Secondary Dark Green
val HealthyGreenDarkPale = Color(0xFF66BB6A) // Pale Dark Green (replaces dark blue light)
val HealthyAccentDark = Color(0xFF689F38) // Dark Accent Color

// Background and Surface Colors
    val HealthyBackground = Color(0xFFF5F9F5) // Light Background, Pale Green Tone
val HealthyBackgroundDark = Color(0xFF1B2A1B) // Dark Background, Deep Green Tone
val HealthySurface = Color(0xFFFFFFFF) // Light Surface
val HealthySurfaceDark = Color(0xFF263226) // Dark Surface

// Text and Icon Colors
val HealthyOnPrimary = Color(0xFFFFFFFF) // Text/Icon on Primary Color
val HealthyOnSurface = Color(0xFF1B1B1B) // Text/Icon on Light Surface
val HealthyOnSurfaceDark = Color(0xFFE1E1E1) // Text/Icon on Dark Surface