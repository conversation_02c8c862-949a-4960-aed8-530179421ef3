package com.fit2081.nutritrack.utils

import android.content.Context
import com.fit2081.nutritrack.models.QuestionnaireAnswers

// Function to save questionnaire data to SharedPreferences
fun saveQuestionnaireData(context: Context, answers: QuestionnaireAnswers) {
    // Get user ID to create user-specific keys
    val userId = getUserId(context)
    // Implementation to save data using SharedPreferences
    val sharedPreferences = context.getSharedPreferences("NutriTrackPrefs", Context.MODE_PRIVATE)
    with(sharedPreferences.edit()) {
        putStringSet("${userId}_foodCategories", answers.foodCategories.toSet())
        putString("${userId}_persona", answers.persona)
        putString("${userId}_biggestMealTime", answers.biggestMealTime)
        putString("${userId}_sleepTime", answers.sleepTime)
        putString("${userId}_wakeUpTime", answers.wakeUpTime)
        apply()
    }
}

// Function to retrieve questionnaire data from SharedPreferences
fun getQuestionnaireData(context: Context): QuestionnaireAnswers? {
    val userId = getUserId(context)
    val sharedPreferences = context.getSharedPreferences("NutriTrackPrefs", Context.MODE_PRIVATE)
    
    // Get user-specific data using the user ID as key prefix
    val foodCategoriesSet = sharedPreferences.getStringSet("${userId}_foodCategories", null)
    val persona = sharedPreferences.getString("${userId}_persona", null)
    val biggestMealTime = sharedPreferences.getString("${userId}_biggestMealTime", null)
    val sleepTime = sharedPreferences.getString("${userId}_sleepTime", null)
    val wakeUpTime = sharedPreferences.getString("${userId}_wakeUpTime", null)
    
    // Return null if any of the required data is missing
    if (foodCategoriesSet == null || persona == null || biggestMealTime == null || 
        sleepTime == null || wakeUpTime == null) {
        return null
    }
    
    return QuestionnaireAnswers(
        foodCategories = foodCategoriesSet.toList(),
        persona = persona,
        biggestMealTime = biggestMealTime,
        sleepTime = sleepTime,
        wakeUpTime = wakeUpTime
    )
} 