package com.fit2081.nutritrack.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.fit2081.nutritrack.R
import com.fit2081.nutritrack.ui.components.CategoryProgressBar
import com.fit2081.nutritrack.ui.components.NavigationItem
import com.fit2081.nutritrack.ui.components.NutriTrackNavigationBar
import com.fit2081.nutritrack.ui.components.ScoreCircle
import com.fit2081.nutritrack.utils.FoodScoreData
import com.fit2081.nutritrack.utils.getFoodScoreDescription
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.runtime.collectAsState
import com.fit2081.nutritrack.ui.viewmodel.AuthViewModel
import com.fit2081.nutritrack.ui.viewmodel.DashboardState
import com.fit2081.nutritrack.ui.viewmodel.DashboardViewModel

@Composable
fun DashboardScreen(
    onEditQuestionnaire: () -> Unit,
    onNavigateToHome: () -> Unit,
    onNavigateToProfile: () -> Unit,
    onNavigateToInfo: () -> Unit,
    onNavigateToSettings: () -> Unit,
    authViewModel: AuthViewModel = hiltViewModel(),
    dashboardViewModel: DashboardViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val currentUserId by authViewModel.currentUserId.collectAsState()
    val dashboardState by dashboardViewModel.dashboardState.collectAsState()
    val foodScoreData by dashboardViewModel.foodScoreData.collectAsState()
    val questionnaireAnswers by dashboardViewModel.questionnaireAnswers.collectAsState()

    // Load dashboard data when user ID is available
    LaunchedEffect(currentUserId) {
        currentUserId?.let { userId ->
            dashboardViewModel.loadDashboardData(userId)
        }
    }

    val personaName = questionnaireAnswers?.persona ?: "Food Lover"
    val isLoading = dashboardState is DashboardState.Loading

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        bottomBar = {
            NutriTrackNavigationBar(
                selectedItem = NavigationItem.HOME,
                onNavigateToHome = onNavigateToHome,
                onNavigateToInsights = onNavigateToProfile,
                onNavigateToNutriCoach = onNavigateToInfo,
                onNavigateToSettings = onNavigateToSettings
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Greeting Section
            Text(
                text = stringResource(R.string.dashboard_welcome, personaName),
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Food Score Card
            Card(
                modifier = Modifier
                    .fillMaxWidth(),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 6.dp
                ),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                shape = RoundedCornerShape(16.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(R.string.food_quality_score_title),
                        style = MaterialTheme.typography.titleLarge,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.padding(16.dp)
                        )
                    } else if (foodScoreData != null) {
                        // Use shared ScoreCircle component
                        ScoreCircle(score = foodScoreData!!.totalScore)

                        Spacer(modifier = Modifier.height(16.dp))

                        // Score Description
                        Text(
                            text = getFoodScoreDescription(foodScoreData!!.totalScore),
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Center
                        )

                        Spacer(modifier = Modifier.height(16.dp))
                        Divider()
                        Spacer(modifier = Modifier.height(16.dp))

                        // Specific Scores
                        Column(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            // Use shared CategoryProgressBar component
                            CategoryProgressBar(
                                categoryName = stringResource(R.string.vegetables_score),
                                score = foodScoreData!!.vegetablesScore,
                                maxScore = 10.0
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            CategoryProgressBar(
                                categoryName = stringResource(R.string.fruit_score),
                                score = foodScoreData!!.fruitScore,
                                maxScore = 10.0
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            CategoryProgressBar(
                                categoryName = stringResource(R.string.discretionary_score),
                                score = foodScoreData!!.discretionaryScore,
                                maxScore = 10.0
                            )
                        }
                    } else {
                        Text(
                            text = stringResource(R.string.no_score_data),
                            style = MaterialTheme.typography.bodyMedium,
                            textAlign = TextAlign.Center,
                            color = Color.Gray
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Score Explanation
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 3.dp
                ),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.what_score_means),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = stringResource(R.string.score_explanation_part1),
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = stringResource(R.string.score_explanation_part2),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Edit Button
            Button(
                onClick = onEditQuestionnaire,
                modifier = Modifier.fillMaxWidth(0.7f)
            ) {
                Icon(
                    Icons.Filled.Edit,
                    contentDescription = "Edit",
                    modifier = Modifier.padding(end = 8.dp)
                )
                Text(
                    text = stringResource(R.string.edit_questionnaire)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}