package com.fit2081.nutritrack.data.repository

import android.content.Context
import com.fit2081.nutritrack.data.database.dao.PatientDao
import com.fit2081.nutritrack.data.database.entities.PatientEntity
import com.fit2081.nutritrack.utils.FoodScoreData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.io.BufferedReader
import java.io.InputStreamReader
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PatientRepository @Inject constructor(
    private val patientDao: PatientDao,
    private val context: Context
) {
    
    suspend fun getPatientByUserId(userId: String): PatientEntity? {
        return patientDao.getPatientByUserId(userId)
    }
    
    fun getPatientByUserIdFlow(userId: String): Flow<PatientEntity?> {
        return patientDao.getPatientByUserIdFlow(userId)
    }
    
    suspend fun validatePatientCredentials(phoneNumber: String, userId: String): PatientEntity? {
        return patientDao.validatePatientCredentials(phoneNumber, userId)
    }
    
    suspend fun getAllUserIds(): List<String> {
        return patientDao.getAllUserIds()
    }
    
    suspend fun insertPatient(patient: PatientEntity) {
        patientDao.insertPatient(patient)
    }
    
    suspend fun updatePatient(patient: PatientEntity) {
        patientDao.updatePatient(patient)
    }
    
    suspend fun deletePatient(patient: PatientEntity) {
        patientDao.deletePatient(patient)
    }
    
    suspend fun getPatientCount(): Int {
        return patientDao.getPatientCount()
    }
    
    // Convert PatientEntity to FoodScoreData for UI compatibility
    fun getFoodScoreDataFlow(userId: String): Flow<FoodScoreData?> {
        return getPatientByUserIdFlow(userId).map { patient ->
            patient?.let { convertToFoodScoreData(it) }
        }
    }
    
    suspend fun getFoodScoreData(userId: String): FoodScoreData? {
        val patient = getPatientByUserId(userId)
        return patient?.let { convertToFoodScoreData(it) }
    }
    
    private fun convertToFoodScoreData(patient: PatientEntity): FoodScoreData {
        val isMale = patient.sex.equals("Male", ignoreCase = true)
        return FoodScoreData(
            userID = patient.userId,
            gender = patient.sex,
            totalScore = if (isMale) patient.heiTotalScoreMale else patient.heiTotalScoreFemale,
            discretionaryScore = if (isMale) patient.discretionaryScoreMale else patient.discretionaryScoreFemale,
            vegetablesScore = if (isMale) patient.vegetablesScoreMale else patient.vegetablesScoreFemale,
            fruitScore = if (isMale) patient.fruitScoreMale else patient.fruitScoreFemale,
            grainsScore = if (isMale) patient.grainsScoreMale else patient.grainsScoreFemale,
            wholeGrainsScore = if (isMale) patient.wholeGrainsScoreMale else patient.wholeGrainsScoreFemale,
            meatScore = if (isMale) patient.meatScoreMale else patient.meatScoreFemale,
            dairyScore = if (isMale) patient.dairyScoreMale else patient.dairyScoreFemale,
            waterScore = if (isMale) patient.waterScoreMale else patient.waterScoreFemale,
            sugarScore = if (isMale) patient.sugarScoreMale else patient.sugarScoreFemale,
            fatScore = if (isMale) patient.saturatedFatScoreMale else patient.saturatedFatScoreFemale
        )
    }
    
    // Load CSV data into database (only on first run)
    suspend fun loadCsvDataIfNeeded() {
        val patientCount = getPatientCount()
        if (patientCount == 0) {
            loadCsvData()
        }
    }
    
    private suspend fun loadCsvData() {
        try {
            val inputStream = context.assets.open("patient_data.csv")
            val reader = BufferedReader(InputStreamReader(inputStream))
            
            // Skip header
            reader.readLine()
            
            val patients = mutableListOf<PatientEntity>()
            var line = reader.readLine()
            
            while (line != null) {
                val values = line.split(",")
                if (values.size >= 60) { // Ensure we have enough columns
                    val patient = PatientEntity(
                        userId = values[1].trim(),
                        phoneNumber = values[0].trim(),
                        sex = values[2].trim(),
                        heiTotalScoreMale = values[3].toDoubleOrNull() ?: 0.0,
                        heiTotalScoreFemale = values[4].toDoubleOrNull() ?: 0.0,
                        discretionaryScoreMale = values[5].toDoubleOrNull() ?: 0.0,
                        discretionaryScoreFemale = values[6].toDoubleOrNull() ?: 0.0,
                        discretionaryServeSize = values[7].toDoubleOrNull() ?: 0.0,
                        vegetablesScoreMale = values[8].toDoubleOrNull() ?: 0.0,
                        vegetablesScoreFemale = values[9].toDoubleOrNull() ?: 0.0,
                        vegetablesWithLegumesServeSize = values[10].toDoubleOrNull() ?: 0.0,
                        legumesAllocatedVegetables = values[11].toDoubleOrNull() ?: 0.0,
                        vegetablesVariationsScore = values[12].toDoubleOrNull() ?: 0.0,
                        vegetablesCruciferous = values[13].toDoubleOrNull() ?: 0.0,
                        vegetablesTuberAndBulb = values[14].toDoubleOrNull() ?: 0.0,
                        vegetablesOther = values[15].toDoubleOrNull() ?: 0.0,
                        legumes = values[16].toDoubleOrNull() ?: 0.0,
                        vegetablesGreen = values[17].toDoubleOrNull() ?: 0.0,
                        vegetablesRedAndOrange = values[18].toDoubleOrNull() ?: 0.0,
                        fruitScoreMale = values[19].toDoubleOrNull() ?: 0.0,
                        fruitScoreFemale = values[20].toDoubleOrNull() ?: 0.0,
                        fruitServeSize = values[21].toDoubleOrNull() ?: 0.0,
                        fruitVariationsScore = values[22].toDoubleOrNull() ?: 0.0,
                        fruitPome = values[23].toDoubleOrNull() ?: 0.0,
                        fruitTropicalAndSubtropical = values[24].toDoubleOrNull() ?: 0.0,
                        fruitBerry = values[25].toDoubleOrNull() ?: 0.0,
                        fruitStone = values[26].toDoubleOrNull() ?: 0.0,
                        fruitCitrus = values[27].toDoubleOrNull() ?: 0.0,
                        fruitOther = values[28].toDoubleOrNull() ?: 0.0,
                        grainsScoreMale = values[29].toDoubleOrNull() ?: 0.0,
                        grainsScoreFemale = values[30].toDoubleOrNull() ?: 0.0,
                        grainsServeSize = values[31].toDoubleOrNull() ?: 0.0,
                        grainsNonWholeGrains = values[32].toDoubleOrNull() ?: 0.0,
                        wholeGrainsScoreMale = values[33].toDoubleOrNull() ?: 0.0,
                        wholeGrainsScoreFemale = values[34].toDoubleOrNull() ?: 0.0,
                        wholeGrainsServeSize = values[35].toDoubleOrNull() ?: 0.0,
                        meatScoreMale = values[36].toDoubleOrNull() ?: 0.0,
                        meatScoreFemale = values[37].toDoubleOrNull() ?: 0.0,
                        meatWithLegumesServeSize = values[38].toDoubleOrNull() ?: 0.0,
                        legumesAllocatedMeat = values[39].toDoubleOrNull() ?: 0.0,
                        dairyScoreMale = values[40].toDoubleOrNull() ?: 0.0,
                        dairyScoreFemale = values[41].toDoubleOrNull() ?: 0.0,
                        dairyServeSize = values[42].toDoubleOrNull() ?: 0.0,
                        sodiumScoreMale = values[43].toDoubleOrNull() ?: 0.0,
                        sodiumScoreFemale = values[44].toDoubleOrNull() ?: 0.0,
                        sodiumMilligrams = values[45].toDoubleOrNull() ?: 0.0,
                        alcoholScoreMale = values[46].toDoubleOrNull() ?: 0.0,
                        alcoholScoreFemale = values[47].toDoubleOrNull() ?: 0.0,
                        alcoholStandardDrinks = values[48].toDoubleOrNull() ?: 0.0,
                        waterScoreMale = values[49].toDoubleOrNull() ?: 0.0,
                        waterScoreFemale = values[50].toDoubleOrNull() ?: 0.0,
                        water = values[51].toDoubleOrNull() ?: 0.0,
                        waterTotalMl = values[52].toDoubleOrNull() ?: 0.0,
                        beverageTotalMl = values[53].toDoubleOrNull() ?: 0.0,
                        sugarScoreMale = values[54].toDoubleOrNull() ?: 0.0,
                        sugarScoreFemale = values[55].toDoubleOrNull() ?: 0.0,
                        sugar = values[56].toDoubleOrNull() ?: 0.0,
                        saturatedFatScoreMale = values[57].toDoubleOrNull() ?: 0.0,
                        saturatedFatScoreFemale = values[58].toDoubleOrNull() ?: 0.0,
                        saturatedFat = values[59].toDoubleOrNull() ?: 0.0,
                        unsaturatedFatScoreMale = if (values.size > 60) values[60].toDoubleOrNull() ?: 0.0 else 0.0,
                        unsaturatedFatScoreFemale = if (values.size > 61) values[61].toDoubleOrNull() ?: 0.0 else 0.0,
                        unsaturatedFatServeSize = if (values.size > 62) values[62].toDoubleOrNull() ?: 0.0 else 0.0
                    )
                    patients.add(patient)
                }
                line = reader.readLine()
            }
            
            reader.close()
            patientDao.insertPatients(patients)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
