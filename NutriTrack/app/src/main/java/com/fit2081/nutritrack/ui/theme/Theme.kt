package com.fit2081.nutritrack.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

// Use health-related green tones
private val HealthyDarkColorScheme = darkColorScheme(
    primary = HealthyGreenDark,
    onPrimary = HealthyOnPrimary,
    primaryContainer = HealthyGreenDarkLight,
    secondary = HealthyGreenDarkMedium,
    secondaryContainer = HealthyGreenDarkPale,
    tertiary = HealthyAccentDark,
    background = HealthyBackgroundDark,
    surface = HealthySurfaceDark,
    onSurface = HealthyOnSurfaceDark
)

private val HealthyLightColorScheme = lightColorScheme(
    primary = HealthyGreen,
    onPrimary = HealthyOnPrimary,
    primaryContainer = HealthyGreenLight,
    secondary = HealthyGreenMedium,
    secondaryContainer = HealthyGreenPale,
    tertiary = HealthyAccent,
    background = HealthyBackground,
    surface = HealthySurface,
    onSurface = HealthyOnSurface
)

@Composable
fun NutriTrackTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+, default is false to use our custom health colors
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> HealthyDarkColorScheme
        else -> HealthyLightColorScheme
    }
    
    // Update the status bar color to match the theme
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.primary.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}