package com.fit2081.nutritrack.data.session

import android.content.Context
import android.content.SharedPreferences
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SessionManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    companion object {
        private const val PREFS_NAME = "nutritrack_session"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val KEY_LOGIN_TIMESTAMP = "login_timestamp"
    }
    
    fun saveUserSession(userId: String) {
        prefs.edit().apply {
            putString(KEY_USER_ID, userId)
            putBoolean(KEY_IS_LOGGED_IN, true)
            putLong(KEY_LOGIN_TIMESTAMP, System.currentTimeMillis())
            apply()
        }
    }
    
    fun getCurrentUserId(): String? {
        return if (isLoggedIn()) {
            prefs.getString(KEY_USER_ID, null)
        } else {
            null
        }
    }
    
    fun isLoggedIn(): Boolean {
        return prefs.getBoolean(KEY_IS_LOGGED_IN, false)
    }
    
    fun getLoginTimestamp(): Long {
        return prefs.getLong(KEY_LOGIN_TIMESTAMP, 0)
    }
    
    fun clearSession() {
        prefs.edit().clear().apply()
    }
    
    fun isSessionValid(): Boolean {
        if (!isLoggedIn()) return false
        
        val loginTime = getLoginTimestamp()
        val currentTime = System.currentTimeMillis()
        val sessionDuration = currentTime - loginTime
        
        // Session expires after 30 days (in milliseconds)
        val maxSessionDuration = 30L * 24 * 60 * 60 * 1000
        
        return sessionDuration < maxSessionDuration
    }
}
