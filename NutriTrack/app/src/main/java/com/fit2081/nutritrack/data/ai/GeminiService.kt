package com.fit2081.nutritrack.data.ai

import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.generationConfig
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GeminiService @Inject constructor() {

    companion object {
        // Note: In production, this should be stored securely (e.g., in BuildConfig or secure storage)
        private const val API_KEY = "AIzaSyDcek-2bt8fffxuz6mlXChG44Fq7qY8M_M" // Replace with your actual API key
        private const val MODEL_NAME = "gemini-2.0-flash"
    }

    private val generativeModel = GenerativeModel(
        modelName = MODEL_NAME,
        apiKey = API_KEY,
        generationConfig = generationConfig {
            temperature = 0.7f
            topK = 40
            topP = 0.95f
            maxOutputTokens = 150
        }
    )

    /**
     * Generate a motivational message about fruit intake
     */
    suspend fun generateMotivationalMessage(): Result<String> {
        return try {
            val prompt = "Generate a short encouraging message to help someone improve their fruit intake."
            val response = generativeModel.generateContent(prompt)
            val message = response.text?.trim() ?: "Keep up the great work with your healthy eating habits!"
            Result.success(message)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Generate a fun food tip
     */
    suspend fun generateFunFoodTip(): Result<String> {
        return try {
            val prompt = "Generate a short, fun and interesting fact about fruits or healthy eating."
            val response = generativeModel.generateContent(prompt)
            val message = response.text?.trim() ?: "Did you know that eating colorful fruits provides different vitamins and antioxidants?"
            Result.success(message)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Generate a personalized motivational message based on user context
     */
    suspend fun generatePersonalizedMessage(userContext: String): Result<String> {
        return try {
            val prompt = "Generate a short encouraging message for someone who $userContext. Focus on fruit intake and healthy eating."
            val response = generativeModel.generateContent(prompt)
            val message = response.text?.trim() ?: "Every small step towards healthier eating counts!"
            Result.success(message)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
