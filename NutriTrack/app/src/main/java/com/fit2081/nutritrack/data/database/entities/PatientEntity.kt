package com.fit2081.nutritrack.data.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "patients")
data class PatientEntity(
    @PrimaryKey
    val userId: String,
    val phoneNumber: String,
    val name: String? = null,
    val sex: String,
    val heiTotalScoreMale: Double,
    val heiTotalScoreFemale: Double,
    val discretionaryScoreMale: Double,
    val discretionaryScoreFemale: Double,
    val discretionaryServeSize: Double,
    val vegetablesScoreMale: Double,
    val vegetablesScoreFemale: Double,
    val vegetablesWithLegumesServeSize: Double,
    val legumesAllocatedVegetables: Double,
    val vegetablesVariationsScore: Double,
    val vegetablesCruciferous: Double,
    val vegetablesTuberAndBulb: Double,
    val vegetablesOther: Double,
    val legumes: Double,
    val vegetablesGreen: Double,
    val vegetablesRedAndOrange: Double,
    val fruitScoreMale: Double,
    val fruitScoreFemale: Double,
    val fruitServeSize: Double,
    val fruitVariationsScore: Double,
    val fruitPome: Double,
    val fruitTropicalAndSubtropical: Double,
    val fruitBerry: Double,
    val fruitStone: Double,
    val fruitCitrus: Double,
    val fruitOther: Double,
    val grainsScoreMale: Double,
    val grainsScoreFemale: Double,
    val grainsServeSize: Double,
    val grainsNonWholeGrains: Double,
    val wholeGrainsScoreMale: Double,
    val wholeGrainsScoreFemale: Double,
    val wholeGrainsServeSize: Double,
    val meatScoreMale: Double,
    val meatScoreFemale: Double,
    val meatWithLegumesServeSize: Double,
    val legumesAllocatedMeat: Double,
    val dairyScoreMale: Double,
    val dairyScoreFemale: Double,
    val dairyServeSize: Double,
    val sodiumScoreMale: Double,
    val sodiumScoreFemale: Double,
    val sodiumMilligrams: Double,
    val alcoholScoreMale: Double,
    val alcoholScoreFemale: Double,
    val alcoholStandardDrinks: Double,
    val waterScoreMale: Double,
    val waterScoreFemale: Double,
    val water: Double,
    val waterTotalMl: Double,
    val beverageTotalMl: Double,
    val sugarScoreMale: Double,
    val sugarScoreFemale: Double,
    val sugar: Double,
    val saturatedFatScoreMale: Double,
    val saturatedFatScoreFemale: Double,
    val saturatedFat: Double,
    val unsaturatedFatScoreMale: Double,
    val unsaturatedFatScoreFemale: Double,
    val unsaturatedFatServeSize: Double
)
