package com.fit2081.nutritrack.utils

/**
 * Utility functions for fruit score evaluation
 */
object FruitScoreUtils {
    
    /**
     * Determine if fruit score is non-optimal (below 70)
     * This threshold indicates when fruit coaching should be available
     */
    fun isFruitScoreNonOptimal(fruitScore: Double): Bo<PERSON>an {
        return fruitScore < 70.0
    }
    
    /**
     * Get fruit score description based on score value
     */
    fun getFruitScoreDescription(fruitScore: Double): String {
        return when {
            fruitScore >= 80 -> "优秀！您的水果摄入量很好。"
            fruitScore >= 70 -> "良好。您的水果摄入基本达标。"
            fruitScore >= 60 -> "一般。建议增加水果摄入量。"
            fruitScore >= 50 -> "需要改善。请多吃水果。"
            else -> "急需改善。强烈建议增加水果摄入。"
        }
    }
    
    /**
     * Get fruit coaching recommendation based on score
     */
    fun getFruitCoachingRecommendation(fruitScore: Double): String {
        return when {
            fruitScore >= 70 -> "继续保持良好的水果摄入习惯！"
            fruitScore >= 50 -> "建议每天至少吃2份水果，如苹果、香蕉、橙子等。"
            else -> "建议每天吃3-4份不同种类的水果，获得更多维生素和纤维。"
        }
    }
}
