package com.fit2081.nutritrack.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.fit2081.nutritrack.R
import com.fit2081.nutritrack.ui.components.NavigationItem
import com.fit2081.nutritrack.ui.components.NutriTrackNavigationBar
import com.fit2081.nutritrack.utils.FoodScoreData
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.runtime.collectAsState
import com.fit2081.nutritrack.ui.viewmodel.AuthViewModel
import com.fit2081.nutritrack.ui.viewmodel.DashboardState
import com.fit2081.nutritrack.ui.viewmodel.DashboardViewModel
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import android.content.Intent
import com.fit2081.nutritrack.ui.components.CategoryProgressBar
import com.fit2081.nutritrack.ui.components.ScoreCircle
import com.fit2081.nutritrack.utils.getFoodScoreDescription

@Composable
fun InsightsScreen(
    onNavigateToHome: () -> Unit,
    onNavigateToInsights: () -> Unit,
    onNavigateToNutriCoach: () -> Unit,
    onNavigateToSettings: () -> Unit,
    authViewModel: AuthViewModel = hiltViewModel(),
    dashboardViewModel: DashboardViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val currentUserId by authViewModel.currentUserId.collectAsState()
    val dashboardState by dashboardViewModel.dashboardState.collectAsState()
    val foodScoreData by dashboardViewModel.foodScoreData.collectAsState()

    // Load dashboard data when user ID is available
    LaunchedEffect(currentUserId) {
        currentUserId?.let { userId ->
            dashboardViewModel.loadDashboardData(userId)
        }
    }

    val isLoading = dashboardState is DashboardState.Loading

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        bottomBar = {
            NutriTrackNavigationBar(
                selectedItem = NavigationItem.INSIGHTS,
                onNavigateToHome = onNavigateToHome,
                onNavigateToInsights = onNavigateToInsights,
                onNavigateToNutriCoach = onNavigateToNutriCoach,
                onNavigateToSettings = onNavigateToSettings
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Insights Title
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.insights_title),
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )

                // Share button
                IconButton(
                    onClick = {
                        if (foodScoreData != null) {
                            // Create content to share
                            val shareText = buildString {
                                append("Your Food Score Report")
                                append("\n\n")
                                append("Total Food Score: ${String.format("%.1f", foodScoreData!!.totalScore)}\n")
                                append("Rating: ${getFoodScoreDescription(foodScoreData!!.totalScore)}")
                                append("\n\n")
                                append("Score Breakdown:")
                                append("\n")
                                append("• Vegetables: ${String.format("%.1f", foodScoreData!!.vegetablesScore)}/10\n")
                                append("• Fruit: ${String.format("%.1f", foodScoreData!!.fruitScore)}/10\n")
                                append("• Grains: ${String.format("%.1f", foodScoreData!!.grainsScore)}/10\n")
                                append("• Whole Grains: ${String.format("%.1f", foodScoreData!!.wholeGrainsScore)}/10\n")
                                append("• Meat & Alternatives: ${String.format("%.1f", foodScoreData!!.meatScore)}/10\n")
                                append("• Dairy: ${String.format("%.1f", foodScoreData!!.dairyScore)}/10\n")
                                append("• Water: ${String.format("%.1f", foodScoreData!!.waterScore)}/10\n")
                                append("• Sugar: ${String.format("%.1f", foodScoreData!!.sugarScore)}/10\n")
                                append("• Fat: ${String.format("%.1f", foodScoreData!!.fatScore)}/10\n")
                                append("• Discretionary: ${String.format("%.1f", foodScoreData!!.discretionaryScore)}/10\n")
                            }

                            val sendIntent = Intent().apply {
                                action = Intent.ACTION_SEND
                                putExtra(Intent.EXTRA_TEXT, shareText)
                                type = "text/plain"
                            }
                            context.startActivity(Intent.createChooser(sendIntent, "Share Food Score"))
                        }
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Share,
                        contentDescription = "Share"
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            if (!isLoading && foodScoreData != null) {
                // Total Food Score Card
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = stringResource(R.string.total_food_score),
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.Center
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // Using shared ScoreCircle component
                        ScoreCircle(score = foodScoreData!!.totalScore)

                        Spacer(modifier = Modifier.height(16.dp))

                        // Score Description
                        Text(
                            text = getFoodScoreDescription(foodScoreData!!.totalScore),
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Center
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Button(
                            onClick = onNavigateToNutriCoach,
                            modifier = Modifier.fillMaxWidth(0.7f)
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Info,
                                contentDescription = "Improve",
                                modifier = Modifier.padding(end = 8.dp)
                            )
                            Text(stringResource(R.string.improve_my_score))
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))
            }

            Text(
                text = stringResource(R.string.insights_description),
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(24.dp))

            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.padding(16.dp)
                )
            } else if (foodScoreData != null) {
                // Food Categories Breakdown Card
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 6.dp
                    ),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        // Using shared CategoryProgressBar component
                        CategoryProgressBar(
                            categoryName = stringResource(R.string.vegetables_score),
                            score = foodScoreData!!.vegetablesScore,
                            maxScore = 10.0
                        )

                        Divider(modifier = Modifier.padding(vertical = 12.dp))

                        CategoryProgressBar(
                            categoryName = stringResource(R.string.fruit_score),
                            score = foodScoreData!!.fruitScore,
                            maxScore = 10.0
                        )

                        Divider(modifier = Modifier.padding(vertical = 12.dp))

                        CategoryProgressBar(
                            categoryName = stringResource(R.string.grains_score),
                            score = foodScoreData!!.grainsScore,
                            maxScore = 10.0
                        )

                        Divider(modifier = Modifier.padding(vertical = 12.dp))

                        CategoryProgressBar(
                            categoryName = stringResource(R.string.whole_grains_score),
                            score = foodScoreData!!.wholeGrainsScore,
                            maxScore = 10.0
                        )

                        Divider(modifier = Modifier.padding(vertical = 12.dp))

                        CategoryProgressBar(
                            categoryName = stringResource(R.string.meat_score),
                            score = foodScoreData!!.meatScore,
                            maxScore = 10.0
                        )

                        Divider(modifier = Modifier.padding(vertical = 12.dp))

                        CategoryProgressBar(
                            categoryName = stringResource(R.string.dairy_score),
                            score = foodScoreData!!.dairyScore,
                            maxScore = 10.0
                        )

                        Divider(modifier = Modifier.padding(vertical = 12.dp))

                        CategoryProgressBar(
                            categoryName = stringResource(R.string.water_score),
                            score = foodScoreData!!.waterScore,
                            maxScore = 10.0
                        )

                        Divider(modifier = Modifier.padding(vertical = 12.dp))

                        CategoryProgressBar(
                            categoryName = stringResource(R.string.sugar_score),
                            score = foodScoreData!!.sugarScore,
                            maxScore = 10.0
                        )

                        Divider(modifier = Modifier.padding(vertical = 12.dp))

                        CategoryProgressBar(
                            categoryName = stringResource(R.string.fat_score),
                            score = foodScoreData!!.fatScore,
                            maxScore = 10.0
                        )

                        Divider(modifier = Modifier.padding(vertical = 12.dp))

                        CategoryProgressBar(
                            categoryName = stringResource(R.string.discretionary_score),
                            score = foodScoreData!!.discretionaryScore,
                            maxScore = 10.0
                        )
                    }
                }
            } else {
                Text(
                    text = stringResource(R.string.no_score_data),
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center,
                    color = Color.Gray
                )
            }

            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}