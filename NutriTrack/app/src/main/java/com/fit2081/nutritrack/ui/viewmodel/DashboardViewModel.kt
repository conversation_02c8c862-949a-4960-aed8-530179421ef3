package com.fit2081.nutritrack.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.fit2081.nutritrack.data.repository.FoodIntakeRepository
import com.fit2081.nutritrack.data.repository.PatientRepository
import com.fit2081.nutritrack.models.QuestionnaireAnswers
import com.fit2081.nutritrack.utils.FoodScoreData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val patientRepository: PatientRepository,
    private val foodIntakeRepository: FoodIntakeRepository
) : ViewModel() {
    
    private val _dashboardState = MutableStateFlow<DashboardState>(DashboardState.Loading)
    val dashboardState: StateFlow<DashboardState> = _dashboardState.asStateFlow()
    
    private val _foodScoreData = MutableStateFlow<FoodScoreData?>(null)
    val foodScoreData: StateFlow<FoodScoreData?> = _foodScoreData.asStateFlow()
    
    private val _questionnaireAnswers = MutableStateFlow<QuestionnaireAnswers?>(null)
    val questionnaireAnswers: StateFlow<QuestionnaireAnswers?> = _questionnaireAnswers.asStateFlow()
    
    fun loadDashboardData(userId: String) {
        viewModelScope.launch {
            try {
                _dashboardState.value = DashboardState.Loading
                
                // Load food score data
                val foodScore = patientRepository.getFoodScoreData(userId)
                _foodScoreData.value = foodScore
                
                // Load questionnaire answers
                val answers = foodIntakeRepository.getQuestionnaireAnswers(userId)
                _questionnaireAnswers.value = answers
                
                _dashboardState.value = DashboardState.Success
            } catch (e: Exception) {
                _dashboardState.value = DashboardState.Error("Failed to load dashboard data: ${e.message}")
            }
        }
    }
    
    fun refreshData(userId: String) {
        loadDashboardData(userId)
    }
}

sealed class DashboardState {
    object Loading : DashboardState()
    object Success : DashboardState()
    data class Error(val message: String) : DashboardState()
}
