package com.fit2081.nutritrack.data.repository

import com.fit2081.nutritrack.data.ai.GeminiService
import com.fit2081.nutritrack.data.database.dao.MotivationalTipDao
import com.fit2081.nutritrack.data.database.entities.MotivationalTipEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MotivationalTipRepository @Inject constructor(
    private val motivationalTipDao: MotivationalTipDao,
    private val geminiService: GeminiService
) {
    
    /**
     * Get all tips for a user
     */
    fun getAllTipsForUser(userId: String): Flow<List<MotivationalTipEntity>> {
        return motivationalTipDao.getAllTipsForUser(userId)
    }
    
    /**
     * Get the latest tip for a user
     */
    suspend fun getLatestTipForUser(userId: String): MotivationalTipEntity? {
        return motivationalTipDao.getLatestTipForUser(userId)
    }
    
    /**
     * Generate and save a new motivational message
     */
    suspend fun generateAndSaveMotivationalMessage(userId: String): Result<MotivationalTipEntity> {
        return try {
            val messageResult = geminiService.generateMotivationalMessage()
            
            messageResult.fold(
                onSuccess = { message ->
                    val tip = MotivationalTipEntity(
                        userId = userId,
                        message = message,
                        prompt = "Generate a short encouraging message to help someone improve their fruit intake."
                    )
                    val tipId = motivationalTipDao.insertTip(tip)
                    Result.success(tip.copy(id = tipId))
                },
                onFailure = { exception ->
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Generate and save a fun food tip
     */
    suspend fun generateAndSaveFunFoodTip(userId: String): Result<MotivationalTipEntity> {
        return try {
            val messageResult = geminiService.generateFunFoodTip()
            
            messageResult.fold(
                onSuccess = { message ->
                    val tip = MotivationalTipEntity(
                        userId = userId,
                        message = message,
                        prompt = "Generate a short, fun and interesting fact about fruits or healthy eating."
                    )
                    val tipId = motivationalTipDao.insertTip(tip)
                    Result.success(tip.copy(id = tipId))
                },
                onFailure = { exception ->
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get tip count for user
     */
    suspend fun getTipCountForUser(userId: String): Int {
        return motivationalTipDao.getTipCountForUser(userId)
    }
    
    /**
     * Delete all tips for user
     */
    suspend fun deleteAllTipsForUser(userId: String) {
        motivationalTipDao.deleteAllTipsForUser(userId)
    }
}
