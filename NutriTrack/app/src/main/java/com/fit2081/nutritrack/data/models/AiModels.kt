package com.fit2081.nutritrack.data.models

import com.fit2081.nutritrack.data.database.entities.MotivationalTipEntity

/**
 * Represents the state of AI tip generation
 */
sealed class AiTipState {
    object Idle : AiTipState()
    object Loading : AiTipState()
    data class Success(val tip: MotivationalTipEntity) : AiTipState()
    data class Error(val message: String) : AiTipState()
}

/**
 * Data class for displaying motivational tips
 */
data class MotivationalTip(
    val id: Long,
    val message: String,
    val createdAt: Long
)

/**
 * Convert entity to display model
 */
fun MotivationalTipEntity.toDisplayModel(): MotivationalTip {
    return MotivationalTip(
        id = this.id,
        message = this.message,
        createdAt = this.createdAt
    )
}
