package com.fit2081.nutritrack.di

import android.content.Context
import com.fit2081.nutritrack.data.database.dao.FoodIntakeDao
import com.fit2081.nutritrack.data.database.dao.PatientDao
import com.fit2081.nutritrack.data.database.dao.UserDao
import com.fit2081.nutritrack.data.repository.FoodIntakeRepository
import com.fit2081.nutritrack.data.repository.PatientRepository
import com.fit2081.nutritrack.data.repository.UserRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object RepositoryModule {
    
    @Provides
    @Singleton
    fun providePatientRepository(
        patientDao: PatientDao,
        @ApplicationContext context: Context
    ): PatientRepository {
        return PatientRepository(patientDao, context)
    }
    
    @Provides
    @Singleton
    fun provideUserRepository(userDao: UserDao): UserRepository {
        return UserRepository(userDao)
    }
    
    @Provides
    @Singleton
    fun provideFoodIntakeRepository(foodIntakeDao: FoodIntakeDao): FoodIntakeRepository {
        return FoodIntakeRepository(foodIntakeDao)
    }
}
